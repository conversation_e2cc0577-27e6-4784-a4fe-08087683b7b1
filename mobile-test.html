<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>MAAC Nashik - Complete Mobile Responsive Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <link rel="stylesheet" href="assets/css/icofont.min.css">
    <link rel="stylesheet" href="assets/css/plugins.css">
    <link rel="stylesheet" href="assets/css/helper.css">
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        /* Test styles for responsiveness */
        .test-section {
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .device-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        .responsive-test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-5px);
        }
        .breakpoint-indicator {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .mobile { background: #ff6b6b; color: white; }
        .tablet { background: #4ecdc4; color: white; }
        .desktop { background: #45b7d1; color: white; }
        .large { background: #96ceb4; color: white; }
    </style>
</head>
<body>
    <!-- Device Info Display -->
    <div class="device-info" id="deviceInfo">
        <div>Screen: <span id="screenSize"></span></div>
        <div>Device: <span id="deviceType"></span></div>
    </div>

    <!-- Updated Responsive Header -->
    <header class="header header-static bg-black header-sticky">
        <div class="default-header menu-right">
            <div class="container container-1520">
                <div class="row align-items-center">
                    
                    <!--Logo start-->
                    <div class="col-12 col-md-4 col-lg-3 order-1 order-md-1">
                        <div class="logo text-center text-md-start">
                            <a href="https://maacnashik.co.in">
                                <img src="assets/images/MAAC Virar Logo-02.png" 
                                     class="img-fluid" 
                                     style="max-height: 120px; height: auto;" 
                                     alt="MAAC Nashik Logo">
                            </a>
                        </div>
                    </div>
                    <!--Logo end-->
                    
                    <!--Menu start-->
                    <div class="col-12 col-md-8 col-lg-9 order-3 order-md-2 d-none d-lg-flex justify-content-center justify-content-lg-end">
                        <nav class="main-menu menu-style-2">
                            <ul class="d-flex flex-wrap justify-content-center justify-content-lg-end">
                                <li><a href="https://maacnashik.co.in">Home</a></li>
                                <li><a href="about-us.php">About Us</a></li>
                                <li><a href="gallery.php">Gallery</a></li>  
                                <li><a href="#!">Our Courses</a>
                                    <ul class="sub-menu">
                                        <li><a href="our-courses.php">Our Courses</a></li>
                                        <li><a href="best-animation-and-vfx-course-in-nashik.php">Animation and VFX Course</a></li>
                                        <li><a href="best-animation-design-course-in-nashik.php">Animation Design Course</a></li>
                                        <li><a href="best-2D-and-3D-animation-course-in-nashik.php">2D and 3D Animation Course</a></li>
                                        <li><a href="best-motion-design-course-in-nashik.php">Motion Design Course</a></li>
                                    </ul>
                                </li>
                                <li><a href="contact-us.php">Contact Us</a></li>
                            </ul>
                        </nav>
                    </div>
                    <!--Menu end-->
                    
                    <!--Mobile Menu Toggle Button-->
                    <div class="col-12 d-lg-none order-2 order-md-3 text-end">
                        <div class="mobile-menu-toggle d-inline-block">
                            <button class="btn btn-link text-white p-2" type="button" id="mobileMenuToggle">
                                <i class="fa fa-bars fa-lg"></i>
                            </button>
                        </div>
                    </div>
                    <!--Mobile Menu Toggle Button End-->
                    
                </div>
                
                <!--Mobile Menu start-->
                <div class="row">
                    <div class="col-12 d-lg-none">
                        <div class="mobile-menu">
                            <nav class="mobile-nav" id="mobileNav" style="display: none;">
                                <ul class="list-unstyled mb-0">
                                    <li class="border-bottom"><a href="https://maacnashik.co.in" class="d-block py-3 px-4 text-decoration-none">Home</a></li>
                                    <li class="border-bottom"><a href="about-us.php" class="d-block py-3 px-4 text-decoration-none">About Us</a></li>
                                    <li class="border-bottom"><a href="gallery.php" class="d-block py-3 px-4 text-decoration-none">Gallery</a></li>
                                    <li class="border-bottom">
                                        <a href="#!" class="d-block py-3 px-4 text-decoration-none" data-bs-toggle="collapse" data-bs-target="#coursesSubmenu">
                                            Our Courses <i class="fa fa-chevron-down float-end mt-1"></i>
                                        </a>
                                        <div class="collapse" id="coursesSubmenu">
                                            <ul class="list-unstyled ps-4 mb-0 bg-light">
                                                <li class="border-bottom"><a href="our-courses.php" class="d-block py-2 px-3 text-decoration-none">Our Courses</a></li>
                                                <li class="border-bottom"><a href="best-animation-and-vfx-course-in-nashik.php" class="d-block py-2 px-3 text-decoration-none">Animation and VFX Course</a></li>
                                                <li class="border-bottom"><a href="best-animation-design-course-in-nashik.php" class="d-block py-2 px-3 text-decoration-none">Animation Design Course</a></li>
                                                <li class="border-bottom"><a href="best-2D-and-3D-animation-course-in-nashik.php" class="d-block py-2 px-3 text-decoration-none">2D and 3D Animation Course</a></li>
                                                <li><a href="best-motion-design-course-in-nashik.php" class="d-block py-2 px-3 text-decoration-none">Motion Design Course</a></li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li><a href="contact-us.php" class="d-block py-3 px-4 text-decoration-none">Contact Us</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
                <!--Mobile Menu end-->
                
            </div>
        </div>
    </header>
    <!-- Header section end -->

    <!-- Test Content -->
    <div class="container mt-5 pt-5">
        <div class="test-section">
            <h1 class="text-center mb-4">🎯 Complete Mobile Responsiveness Test</h1>
            <p class="text-center">This page tests all aspects of mobile responsiveness for the MAAC Nashik website.</p>
        </div>

        <!-- Breakpoint Indicators -->
        <div class="row">
            <div class="col-12">
                <h3>Current Breakpoint:</h3>
                <div class="breakpoint-indicator mobile d-block d-sm-none">📱 Extra Small (< 576px)</div>
                <div class="breakpoint-indicator mobile d-none d-sm-block d-md-none">📱 Small (576px - 767px)</div>
                <div class="breakpoint-indicator tablet d-none d-md-block d-lg-none">📱 Medium/Tablet (768px - 991px)</div>
                <div class="breakpoint-indicator desktop d-none d-lg-block d-xl-none">💻 Large/Desktop (992px - 1199px)</div>
                <div class="breakpoint-indicator large d-none d-xl-block">🖥️ Extra Large (≥ 1200px)</div>
            </div>
        </div>

        <!-- Responsive Grid Test -->
        <div class="responsive-test-grid">
            <div class="test-card">
                <h4>📱 Mobile Features</h4>
                <ul>
                    <li>✅ Responsive Logo</li>
                    <li>✅ Mobile Menu Toggle</li>
                    <li>✅ Touch-Friendly Navigation</li>
                    <li>✅ Optimized Font Sizes</li>
                </ul>
            </div>
            <div class="test-card">
                <h4>🎨 Design Elements</h4>
                <ul>
                    <li>✅ Flexible Images</li>
                    <li>✅ Responsive Typography</li>
                    <li>✅ Adaptive Layouts</li>
                    <li>✅ Consistent Spacing</li>
                </ul>
            </div>
            <div class="test-card">
                <h4>⚡ Performance</h4>
                <ul>
                    <li>✅ Fast Loading</li>
                    <li>✅ Optimized Images</li>
                    <li>✅ Minimal JavaScript</li>
                    <li>✅ CSS Optimization</li>
                </ul>
            </div>
        </div>

        <!-- Bootstrap Grid Test -->
        <div class="row">
            <div class="col-12 col-md-6 col-lg-4 mb-3">
                <div class="test-section">
                    <h5>Column 1</h5>
                    <p>This column adapts to different screen sizes using Bootstrap's responsive grid system.</p>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4 mb-3">
                <div class="test-section">
                    <h5>Column 2</h5>
                    <p>On mobile, these columns stack vertically. On tablet, they show 2 per row. On desktop, 3 per row.</p>
                </div>
            </div>
            <div class="col-12 col-md-12 col-lg-4 mb-3">
                <div class="test-section">
                    <h5>Column 3</h5>
                    <p>The responsive behavior ensures optimal viewing experience across all devices.</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Testing Checklist</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Mobile (< 768px)</h5>
                    <ul>
                        <li>Logo scales appropriately</li>
                        <li>Mobile menu appears</li>
                        <li>Content stacks vertically</li>
                        <li>Text remains readable</li>
                        <li>Touch targets are adequate</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>Desktop (≥ 992px)</h5>
                    <ul>
                        <li>Horizontal navigation visible</li>
                        <li>Logo at optimal size</li>
                        <li>Multi-column layouts</li>
                        <li>Hover effects work</li>
                        <li>Dropdown menus function</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/plugins.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Display current screen size and device type
        function updateScreenSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            let deviceType = '';
            
            if (width < 576) deviceType = 'Extra Small Mobile';
            else if (width < 768) deviceType = 'Small Mobile';
            else if (width < 992) deviceType = 'Tablet';
            else if (width < 1200) deviceType = 'Desktop';
            else deviceType = 'Large Desktop';
            
            document.getElementById('screenSize').textContent = `${width}x${height}`;
            document.getElementById('deviceType').textContent = deviceType;
        }
        
        // Mobile Menu Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileNav = document.getElementById('mobileNav');
            
            if (mobileMenuToggle && mobileNav) {
                mobileMenuToggle.addEventListener('click', function() {
                    if (mobileNav.style.display === 'none' || mobileNav.style.display === '') {
                        mobileNav.style.display = 'block';
                        mobileMenuToggle.innerHTML = '<i class="fa fa-times fa-lg"></i>';
                    } else {
                        mobileNav.style.display = 'none';
                        mobileMenuToggle.innerHTML = '<i class="fa fa-bars fa-lg"></i>';
                    }
                });
            }
            
            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (mobileNav && mobileMenuToggle) {
                    if (!mobileNav.contains(event.target) && !mobileMenuToggle.contains(event.target)) {
                        mobileNav.style.display = 'none';
                        mobileMenuToggle.innerHTML = '<i class="fa fa-bars fa-lg"></i>';
                    }
                }
            });
            
            // Close mobile menu on window resize to desktop
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 992 && mobileNav) {
                    mobileNav.style.display = 'none';
                    if (mobileMenuToggle) {
                        mobileMenuToggle.innerHTML = '<i class="fa fa-bars fa-lg"></i>';
                    }
                }
                updateScreenSize();
            });
            
            // Initial call
            updateScreenSize();
        });
    </script>

    <!-- Enhanced Mobile Responsiveness Styles -->
    <style>
        @media (max-width: 991.98px) {
            .logo img {
                max-height: 80px !important;
            }
            
            .mobile-nav {
                background: #ffffff;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                margin-top: 10px;
                border-radius: 8px;
            }
            
            .mobile-nav a {
                color: #333 !important;
                font-weight: 500;
                transition: all 0.3s ease;
            }
            
            .mobile-nav a:hover {
                background-color: #f8f9fa !important;
                color: #061da4 !important;
            }
            
            .mobile-menu-toggle button {
                border: none !important;
                background: transparent !important;
            }
            
            .mobile-menu-toggle button:focus {
                box-shadow: none !important;
            }
        }
        
        @media (max-width: 767.98px) {
            .logo {
                text-align: center !important;
            }
            
            .logo img {
                max-height: 70px !important;
            }
            
            .header {
                padding: 10px 0;
            }
        }
        
        @media (max-width: 575.98px) {
            .logo img {
                max-height: 60px !important;
            }
            
            .container-1520 {
                padding-left: 15px;
                padding-right: 15px;
            }
        }
        
        @media (max-width: 479.98px) {
            .logo img {
                max-height: 50px !important;
            }
        }
        
        @media (min-width: 992px) {
            .main-menu ul li a {
                padding: 15px 20px;
                font-weight: 500;
                transition: all 0.3s ease;
            }
            
            .main-menu ul li a:hover {
                color: #061da4 !important;
            }
        }
    </style>
</body>
</html>
