  
    <!--Footer section start-->
    <footer class="footer-section style-2 section bg-theme" style="background-image: url(assets/images/bg/footer-bg.jpg)">
       

        
        <!--Footer bottom start-->
        <div class="footer-bottom border-color section">
            <div class="container">
                <div class="row">
                    <div class="col-xl-4 col-lg-4 col-md-12 text-lg-left text-md-left text-center">
                        <div class="copyright">
                            <p>&copy;<?php echo date("Y"); ?> <a  href="#">MAAC</a>. All rights reserved. Designed by <a href="https://infinite-vision.co.in" target="blank">Infinite Vision</a></p>
                        </div>
                    </div>
                    <div class="col-xl-4 col-lg-4 col-md-12">
                        <div class="footer-widget float-end">
                            <div class="footer-social">
                               <span>Contact Us :</span>
                                <ul>
                            
                                    <li><a href="tel:918484052227"><p style="font-size: smaller;"><i class="icofont-phone"></i> +918484052227</p></a></li>
                                    <li><a href="tel:918793052227"><p style="font-size: smaller;">+91 8793052227</p></a></li><br>
                                    <li><a href="mailto:<EMAIL>"><p style="font-size: smaller;"><i class="icofont-envelope"></i> <EMAIL></p></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 col-lg-4 col-md-12">
                        <div class="footer-widget float-end">
                            <div class="footer-social">
                               <span>Address :</span>
                                <ul>
                                    <li><a href="#!"><p style="font-size: smaller;">4th Floor, Siddhi Pooja Trade Center, Opp. BYK College, College Road, Nashik, Maharshtra, 422005</p></a></li>
                                    <li><a href="javascript:void(0)">GSTIN: 27CRDPP2116A2ZA</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--Footer bottom end-->
         <!-- whatsapp code start -->
             <div class="whatsapp">
                <a href="https://web.whatsapp.com/send?phone=917249448095" target="blank">
                     <a href="https://web.whatsapp.com/send?phone=917249448095" target="blank"> 
                    <img src="assets/images/whatsapp-icon.png" width="80px" height="80px" alt="whatsapp">
                </a> 
             </div> 
        <!-- whatsapp code end -->
        
     </footer>
     <!--Footer section end-->
    
</div>

<!-- Placed js at the end of the document so the pages load faster -->

<!-- All jquery file included here -->
<script src="assets/js/vendor/modernizr-3.6.0.min.js"></script>
<script src="assets/js/vendor/jquery-3.6.0.min.js"></script>
<script src="assets/js/vendor/jquery-migrate-3.3.2.min.js"></script>

<script src="assets/js/bootstrap.min.js"></script>
<script src="assets/js/plugins.js"></script>
<script src="assets/js/main.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.0/jquery.min.js" integrity="sha512-3gJwYpMe3QewGELv8k/BX9vcqhryRdzRMxVfq6ngyWXwo03GFEzjsUm8Q7RZcHPHksttq7/GFoxjCVUjkjvPdw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <script>
        function validateForm() {
            // Get the phone number input value
            var phoneInput = document.getElementsByName('phone')[0];
            var phoneValue = phoneInput.value;

            // Check if the phone number has exactly 10 digits
            if (!/^\d{10}$/.test(phoneValue)) {
                // Display a warning message
                document.getElementById('mobileError').style.color = 'red';
                document.getElementById('mobileError').innerText = 'Please enter a 10-digit phone number.';
                return false; // Prevent the form submission
            }

            // If the phone number is valid, clear any previous warning and allow form submission
            document.getElementById('mobileError').innerText = '';
            return true;
        }
    </script>
    <script>
        function validateContactPage() {
            // Get the phone number input value
            var phoneInput = document.getElementsByName('phone')[0];
            var phoneValue = phoneInput.value;

            // Check if the phone number has exactly 10 digits
            if (!/^\d{10}$/.test(phoneValue)) {
                // Display a warning message
                document.getElementById('mobileError').style.color = 'white';
                document.getElementById('mobileError').innerText = 'Please enter a 10-digit phone number.';
                return false; // Prevent the form submission
            }

            // If the phone number is valid, clear any previous warning and allow form submission
            document.getElementById('mobileError').innerText = '';
            return true;
        }
    </script>

    
</body>



</html>