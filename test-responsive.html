<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>MAAC Nashik - Mobile Responsiveness Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <link rel="stylesheet" href="assets/css/icofont.min.css">
    <link rel="stylesheet" href="assets/css/plugins.css">
    <link rel="stylesheet" href="assets/css/helper.css">
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        /* Test styles for responsiveness */
        .test-section {
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .device-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <!-- Device Info Display -->
    <div class="device-info" id="deviceInfo">
        Screen: <span id="screenSize"></span>
    </div>

    <!-- Include the updated navbar -->
    <?php include 'navbar.php'; ?>

    <!-- Test Content -->
    <div class="container mt-5 pt-5">
        <div class="test-section">
            <h2>Mobile Responsiveness Test</h2>
            <p>This page tests the mobile responsiveness of the MAAC Nashik website.</p>
        </div>
        
        <div class="row">
            <div class="col-12 col-md-6 col-lg-4">
                <div class="test-section">
                    <h3>Column 1</h3>
                    <p>This should stack on mobile devices.</p>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-4">
                <div class="test-section">
                    <h3>Column 2</h3>
                    <p>This should stack on mobile devices.</p>
                </div>
            </div>
            <div class="col-12 col-md-12 col-lg-4">
                <div class="test-section">
                    <h3>Column 3</h3>
                    <p>This should stack on mobile devices.</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Breakpoint Information</h3>
            <ul>
                <li><strong>Mobile:</strong> 0px - 575px</li>
                <li><strong>Small Mobile:</strong> 576px - 767px</li>
                <li><strong>Tablet:</strong> 768px - 991px</li>
                <li><strong>Desktop:</strong> 992px - 1199px</li>
                <li><strong>Large Desktop:</strong> 1200px+</li>
            </ul>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/plugins.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // Display current screen size
        function updateScreenSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            let deviceType = '';
            
            if (width <= 575) deviceType = 'Mobile';
            else if (width <= 767) deviceType = 'Small Mobile';
            else if (width <= 991) deviceType = 'Tablet';
            else if (width <= 1199) deviceType = 'Desktop';
            else deviceType = 'Large Desktop';
            
            document.getElementById('screenSize').textContent = `${width}x${height} (${deviceType})`;
        }
        
        // Update on load and resize
        window.addEventListener('load', updateScreenSize);
        window.addEventListener('resize', updateScreenSize);
    </script>
</body>
</html>
