<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Google Ads Tracking Verification - MAAC Nashik</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- Google tag (gtag.js) - Google Ads -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-17198455088"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'AW-17198455088');
        
        // Log for verification
        console.log('Google Ads Global Site Tag loaded with ID: AW-17198455088');
    </script>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    
    <style>
        body {
            font-family: 'Ubuntu', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .verification-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 800px;
        }
        .status-card {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .check-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .check-item i {
            margin-right: 10px;
            width: 20px;
        }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        .btn-test {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            margin: 10px 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            background: #764ba2;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="verification-container">
            <h1 class="text-center mb-4">🎯 Google Ads Tracking Verification</h1>
            <p class="text-center text-muted">Verify that Google Ads Global Site Tag is properly implemented across all pages</p>
            
            <!-- Implementation Status -->
            <div class="status-card">
                <h3><i class="fa fa-check-circle success"></i> Implementation Status</h3>
                <div class="check-item">
                    <i class="fa fa-check success"></i>
                    <span><strong>Global Site Tag ID:</strong> AW-17198455088</span>
                </div>
                <div class="check-item">
                    <i class="fa fa-check success"></i>
                    <span><strong>Implementation Method:</strong> gtag.js (Recommended)</span>
                </div>
                <div class="check-item">
                    <i class="fa fa-check success"></i>
                    <span><strong>Placement:</strong> In &lt;head&gt; section before other scripts</span>
                </div>
            </div>

            <!-- Files Updated -->
            <div class="status-card">
                <h3><i class="fa fa-file-code-o success"></i> Files Updated</h3>
                <div class="check-item">
                    <i class="fa fa-check success"></i>
                    <span><strong>header.php</strong> - Main header file (used by most pages)</span>
                </div>
                <div class="check-item">
                    <i class="fa fa-check success"></i>
                    <span><strong>success.php</strong> - Standalone success page</span>
                </div>
                <div class="check-item">
                    <i class="fa fa-check success"></i>
                    <span><strong>mobile-test.html</strong> - Mobile test page</span>
                </div>
                <div class="check-item">
                    <i class="fa fa-check success"></i>
                    <span><strong>test-responsive.html</strong> - Responsive test page</span>
                </div>
            </div>

            <!-- Code Implementation -->
            <div class="status-card">
                <h3><i class="fa fa-code success"></i> Code Implementation</h3>
                <p>The following code has been added to all pages:</p>
                <div class="code-block">
&lt;!-- Google tag (gtag.js) - Google Ads --&gt;
&lt;script async src="https://www.googletagmanager.com/gtag/js?id=AW-17198455088"&gt;&lt;/script&gt;
&lt;script&gt;
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'AW-17198455088');
&lt;/script&gt;
                </div>
            </div>

            <!-- Pages Covered -->
            <div class="status-card">
                <h3><i class="fa fa-globe success"></i> Pages Covered</h3>
                <p>Since most pages use <code>header.php</code>, the following pages automatically include the Google Ads tag:</p>
                <div class="row">
                    <div class="col-md-6">
                        <ul>
                            <li>index.php (Homepage)</li>
                            <li>about-us.php</li>
                            <li>gallery.php</li>
                            <li>our-courses.php</li>
                            <li>contact-us.php</li>
                            <li>landing_page.php</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul>
                            <li>best-animation-and-vfx-course-in-nashik.php</li>
                            <li>best-animation-design-course-in-nashik.php</li>
                            <li>best-2D-and-3D-animation-course-in-nashik.php</li>
                            <li>best-motion-design-course-in-nashik.php</li>
                            <li>success.php (standalone)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Verification Tests -->
            <div class="status-card">
                <h3><i class="fa fa-flask warning"></i> Verification Tests</h3>
                <p>Use these methods to verify the implementation:</p>
                
                <h5>1. Browser Developer Tools</h5>
                <ol>
                    <li>Open any page on your website</li>
                    <li>Press F12 to open Developer Tools</li>
                    <li>Go to Console tab</li>
                    <li>Look for: "Google Ads Global Site Tag loaded with ID: AW-17198455088"</li>
                    <li>Go to Network tab and look for requests to googletagmanager.com</li>
                </ol>

                <h5>2. Google Tag Assistant</h5>
                <ol>
                    <li>Install Google Tag Assistant Chrome extension</li>
                    <li>Visit your website</li>
                    <li>Click the Tag Assistant icon</li>
                    <li>Verify Google Ads tag is detected and working</li>
                </ol>

                <h5>3. Google Ads Interface</h5>
                <ol>
                    <li>Go to Google Ads dashboard</li>
                    <li>Navigate to Tools & Settings > Measurement > Conversions</li>
                    <li>Check if the tag status shows as "Active"</li>
                </ol>
            </div>

            <!-- Test Buttons -->
            <div class="text-center mt-4">
                <h4>Test Implementation</h4>
                <button class="btn-test" onclick="testGtagFunction()">Test gtag Function</button>
                <button class="btn-test" onclick="testDataLayer()">Test DataLayer</button>
                <button class="btn-test" onclick="checkNetworkRequests()">Check Network</button>
            </div>

            <!-- Test Results -->
            <div id="testResults" class="mt-4"></div>

            <!-- Next Steps -->
            <div class="status-card">
                <h3><i class="fa fa-arrow-right success"></i> Next Steps</h3>
                <ol>
                    <li><strong>Verify Implementation:</strong> Use the verification methods above</li>
                    <li><strong>Set Up Conversions:</strong> Create conversion actions in Google Ads</li>
                    <li><strong>Test Tracking:</strong> Perform test actions on your website</li>
                    <li><strong>Monitor Performance:</strong> Check Google Ads reports for data</li>
                </ol>
            </div>

            <!-- Important Notes -->
            <div class="status-card warning">
                <h3><i class="fa fa-exclamation-triangle warning"></i> Important Notes</h3>
                <ul>
                    <li>It may take 24-48 hours for data to appear in Google Ads</li>
                    <li>The tag is now on all pages that use header.php</li>
                    <li>success.php has its own implementation since it doesn't use header.php</li>
                    <li>Test pages (mobile-test.html, test-responsive.html) also include the tag</li>
                    <li>Make sure to test the implementation before going live</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Test Functions
        function testGtagFunction() {
            const results = document.getElementById('testResults');
            if (typeof gtag === 'function') {
                results.innerHTML = '<div class="status-card"><i class="fa fa-check success"></i> ✅ gtag function is available and working!</div>';
                console.log('gtag function test passed');
            } else {
                results.innerHTML = '<div class="status-card error"><i class="fa fa-times error"></i> ❌ gtag function not found!</div>';
                console.error('gtag function test failed');
            }
        }

        function testDataLayer() {
            const results = document.getElementById('testResults');
            if (window.dataLayer && Array.isArray(window.dataLayer)) {
                results.innerHTML = '<div class="status-card"><i class="fa fa-check success"></i> ✅ dataLayer is initialized and working!</div>';
                console.log('dataLayer test passed:', window.dataLayer);
            } else {
                results.innerHTML = '<div class="status-card error"><i class="fa fa-times error"></i> ❌ dataLayer not found or not an array!</div>';
                console.error('dataLayer test failed');
            }
        }

        function checkNetworkRequests() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="status-card"><i class="fa fa-info-circle"></i> 📡 Check the Network tab in Developer Tools for requests to googletagmanager.com</div>';
            console.log('Check Network tab for Google Tag Manager requests');
        }

        // Auto-run basic verification on page load
        window.addEventListener('load', function() {
            console.log('=== Google Ads Tracking Verification ===');
            console.log('Conversion ID: AW-17198455088');
            console.log('gtag function available:', typeof gtag === 'function');
            console.log('dataLayer available:', !!window.dataLayer);
            console.log('dataLayer contents:', window.dataLayer);
        });
    </script>
</body>
</html>
